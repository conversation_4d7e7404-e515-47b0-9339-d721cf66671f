using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Xml;
using LitJson;
using System;
using UnityEngine.Profiling;
using UnityEngine.Assertions;

public class ConfigManager
{

    protected Dictionary<object, ConfigData> dataDictList;

    public ConfigManager()
    {
        dataDictList = new Dictionary<object, ConfigData>();
    }

    public virtual void LoadAssetBundle<T>(string assetBundlePath, string assetBundleName, Action<bool> loadedCallBack) where T : ConfigData, new()
    {
        // 新的SO路径格式：[TableName]SO.asset
        string soPath = $"DataSO/{assetBundleName}SO";

        AssetBundleManager.LoadObject(soPath, (obj) =>
        {
            Profiler.BeginSample("readSO:" + assetBundleName);
            if (obj != null)
            {
                ConfigData[] data = null;

                // 使用抽象的BaseSO接口
                if (obj is BaseSO baseSO)
                {
                    data = baseSO.datas;
                }

                Assert.IsNotNull(data, $"{assetBundleName}:SO data is null");
                InitByJson<T>(data);
                Profiler.EndSample();
                loadedCallBack?.Invoke(true);
            }
            else
            {
                Debug.LogError($"Failed to load SO: {soPath}");
                loadedCallBack?.Invoke(false);
            }
        });

        // AssetBundleManager.LoadText(assetBundlePath, (string text) =>
        // {
        //     if (text == null)
        //     {
        //         loadedCallBack?.Invoke(false);
        //     }
        //     else
        //     {
        //         Profiler.BeginSample("parseJson:" + assetBundleName);
        //         var json = LitJson.JsonMapper.ToObject(text);
        //         Profiler.EndSample();
        //         Profiler.BeginSample("readTable:" + assetBundleName);
        //         InitByJson<T>(json);
        //         Profiler.EndSample();
        //         loadedCallBack?.Invoke(true);
        //     }
        //     AssetBundleManager.Release(assetBundlePath);
        // });
    }

    public virtual void InitByJson<T>(ConfigData[] json) where T : ConfigData, new()
    {
        dataDictList.Clear();
        for (int i = 0; i < json.Length; i++)
        {
            var data = json[i];
            AddData(data);
        }
    }

    public virtual void InitByJson<T>(JsonData json) where T : ConfigData, new()
    {
        dataDictList.Clear();

        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Init(i, json[i]);
                AddData(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Init(key, json[key]);
                AddData(data);
            }
        }
    }

    public virtual void InitByXML<T>(string xmlPath) where T : ConfigData, new()
    {
        dataDictList.Clear();

        TextAsset textAsset = (TextAsset)Resources.Load(xmlPath);

        XmlDocument xml = new XmlDocument();
        xml.LoadXml(textAsset.text);

        XmlNode mapNode = xml.SelectSingleNode("data");

        XmlNodeList nodeList = mapNode.SelectNodes("item");
        for (int i = 0; i < nodeList.Count; i++)
        {
            T data = new T();
            data.Init((XmlElement)nodeList[i]);
            AddData(data);
        }
    }

    protected virtual void AddData(ConfigData data)
    {
        if (!dataDictList.ContainsKey(data.uniqueKey))
            dataDictList.Add(data.uniqueKey, data);
        else
            Debug.LogError(data.GetType().ToString() + " : " + data.key + " has duplicate");
    }

    public virtual T GetData<T>(object id) where T : ConfigData
    {
        dataDictList.TryGetValue(id.ToString(), out ConfigData value);
        return (T)value;
    }

    public virtual List<T> GetDataList<T>() where T : ConfigData
    {
        List<T> result = new List<T>();
        foreach (var item in dataDictList)
        {
            result.Add((T)item.Value);
        }
        return result;
    }
}


public class ConfigHelper
{
    private static Dictionary<int, ConfigManager> dataManagerDict;
    public static T GetManager<T>() where T : ConfigManager, new()
    {
        if (dataManagerDict == null)
            dataManagerDict = new Dictionary<int, ConfigManager>();

        ConfigManager dataManager = null;
        int key = typeof(T).GetHashCode();

        dataManagerDict.TryGetValue(key, out dataManager);

        if (dataManager == null)
        {
            dataManager = new T();
            dataManagerDict.Add(key, dataManager);
        }
        return (T)dataManager;
    }
}
