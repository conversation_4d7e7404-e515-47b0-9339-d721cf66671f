using System;
using LitJson;
[Serializable]
public class InfoBattleGuide : ConfigData
{
    public int id;
    public int gateId;
    public int time;
    public string heroInfo;
    public int guideId;
    public string panel;
    public int step;
    public string passType;
    public override object key { get => id; protected set => base.key = value; }
    override public void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        gateId = JsonUtil.ToInt(data, "gateId");
        time = JsonUtil.ToInt(data, "time");
        heroInfo = JsonUtil.ToString(data, "heroInfo");
        guideId = JsonUtil.ToInt(data, "guideId");
        panel = JsonUtil.ToString(data, "panel");
        step = JsonUtil.ToInt(data, "step");
        passType = JsonUtil.ToString(data, "passType");
    }
}