﻿
using Google.Protobuf.WellKnownTypes;
using LitJson;

using System;
[Serializable]
public class InfoBuff : ConfigData
{
    public int id;
    // public string name;
    // public string desc;
    // public string icon;
    public int level;

    public int value;                   //数值
    public float floatValue;            //浮点数值

    public int effect;                  //效果
    public int effectGroup;             //效果组，为0的时候表示对所有效果类型生效
    public int param0;                  //效果参数0
    public int param1;                  //效果参数1
    public string strParams;            //字符串参数
    public float duration;              //持续时间，持续时间小于0表示永久
    public BuffTimeStackType timeStackType;           //时间叠加类型
    public BuffValueStackType valueStackType;          //数值叠加类型
    public string aniName;              //对应的动画表现
    public float exeInterval;           //执行间隔
    public int exeCount;                //执行次数

    public bool IsForever
    {
        get { return timeStackType == BuffTimeStackType.Forever; }
    }

    public override object key { get => id; protected set => base.key = value; }

    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        // name = JsonUtil.ToString(data, "name");
        // desc = JsonUtil.ToString(data, "desc");
        // icon = JsonUtil.ToString(data, "icon");
        level = JsonUtil.ToInt(data, "level");
        value = JsonUtil.ToInt(data, "value");
        floatValue = JsonUtil.ToFloat(data, "floatValue");
        effect = JsonUtil.ToInt(data, "effect");
        effectGroup = JsonUtil.ToInt(data, "effectGroup");
        param0 = JsonUtil.ToInt(data, "param0");
        param1 = JsonUtil.ToInt(data, "param1");
        strParams = JsonUtil.ToString(data, "strParams");
        duration = JsonUtil.ToInt(data, "duration");
        timeStackType = (BuffTimeStackType)JsonUtil.ToInt(data, "timeStackType");
        valueStackType = (BuffValueStackType)JsonUtil.ToInt(data, "valueStackType");
        aniName = JsonUtil.ToString(data, "aniName");
        exeInterval = JsonUtil.ToFloat(data, "exeInterval");
    }

    public InfoBuff GetCopy()
    {
        InfoBuff other = (InfoBuff)this.MemberwiseClone();
        // other.name = String.Copy(this.name);
        // other.desc = String.Copy(this.desc);
        // other.icon = String.Copy(this.icon);
        other.strParams = String.Copy(this.strParams);
        other.aniName = String.Copy(this.aniName);
        return other;
    }
}