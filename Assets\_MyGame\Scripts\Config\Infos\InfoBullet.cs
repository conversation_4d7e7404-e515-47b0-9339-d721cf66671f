﻿using LitJson;
using System;
[Serializable]
public class InfoBullet : ConfigData
{
    public int id;                      //子弹ID
    public string name;
    public int bulletEffectGroup;       //子弹效果分组（BUFF影响的子弹效果分组）
    public string bulletDir;
    public string bulletRes;            //子弹资源名
    public SelectorType selectorType;   //索敌类型 
    public int bulletLayerLevel = 0;         //子弹等级
    public bool fireOnEquip;
    public float delayFireTime;

    public float scale;
    public float cdTime;
    public float lifeTime;
    public int bulletCount;
    public float bulletInterval;
    public float damageMultiplier;
    public float hitInterval;
    public float moveSpeed;
    public float rotRadius;
    public bool rebound;                //是否反弹
    public int reboundEnemyMax;         //命中敌人最大反弹次数
    public float reboundWeakenPercent; //伤害衰减百分比
    public int hitEnemyMax;             //命中敌人最大次数
    public string fireSound;
    public string hitSound;
    public string hitEffect;

    public string subEmitorRes;        //子弹生命周期的二级子弹发射器
    public int subBulletId;            //子弹生命周期的二级子弹ID

    public string emitorResOnRemove;        //子弹移除时候产生的二级子弹发射器
    public int bulletIdOnRemove;            //子弹移除时候产生的二级子弹


    public override object key { get => id; protected set => base.key = value; }

    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        name = JsonUtil.ToString(data, "name");
        bulletEffectGroup = JsonUtil.ToInt(data, "bulletEffectGroup");
        bulletDir = JsonUtil.ToString(data, "bulletDir");
        bulletRes = JsonUtil.ToString(data, "bulletRes");
        selectorType = (SelectorType)JsonUtil.ToInt(data, "selectorType");
        scale = JsonUtil.ToFloat(data, "scale");
      
        fireOnEquip = JsonUtil.ToInt(data, "fireOnEquip") == 1;
        delayFireTime = JsonUtil.ToFloat(data, "delayFireTime");
        cdTime = JsonUtil.ToFloat(data, "cdTime");
        lifeTime = JsonUtil.ToFloat(data, "lifeTime");
        bulletCount = JsonUtil.ToInt(data, "bulletCount");
        bulletInterval = JsonUtil.ToFloat(data, "bulletInterval");
        damageMultiplier = JsonUtil.ToInt(data, "damageMultiplier") * 0.01f;
        hitInterval = JsonUtil.ToFloat(data, "hitInterval");
        moveSpeed = JsonUtil.ToFloat(data, "moveSpeed");
        rotRadius = JsonUtil.ToFloat(data, "rotRadius");
        rebound = JsonUtil.ToInt(data, "rebound") == 1;
        reboundEnemyMax = JsonUtil.ToInt(data, "reboundEnemyMax");
        reboundWeakenPercent = JsonUtil.ToFloat(data, "reboundWeakenPercent");
#if UNITY_EDITOR
        if (reboundWeakenPercent >= 1)
        {
            UnityEngine.Debug.LogError("子弹伤害衰减百分比不能超过100%");
        }
#endif
        hitEnemyMax = JsonUtil.ToInt(data, "hitEnemyMax");
        fireSound = JsonUtil.ToString(data, "fireSound");
        hitSound = JsonUtil.ToString(data, "hitSound");
        hitEffect = JsonUtil.ToString(data, "hitEffect");

        subEmitorRes = JsonUtil.ToString(data, "subEmitorRes");
        subBulletId = JsonUtil.ToInt(data, "subBulletId");

        emitorResOnRemove = JsonUtil.ToString(data, "emitorResOnRemove");
        bulletIdOnRemove = JsonUtil.ToInt(data, "bulletIdOnRemove");

        bulletLayerLevel = JsonUtil.ToInt(data, "bulletLayerLevel");

    }
}