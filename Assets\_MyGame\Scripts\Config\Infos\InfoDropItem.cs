﻿using LitJson;
using System;
using UnityEngine;
[Serializable]
public class InfoDropItem : ConfigData
{
    public int id;
    public int dropId;
    public DropItemType dropItemType;
    public int dropProType;
    public int probability;
    public int value;

    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        dropId = JsonUtil.ToInt(data, "dropId");
        dropItemType = (DropItemType)JsonUtil.ToInt(data, "dropItemType");
        dropProType = JsonUtil.ToInt(data, "dropProType");
        probability = JsonUtil.ToInt(data, "probability");
        value = JsonUtil.ToInt(data, "value");
        _res = JsonUtil.ToString(data, "res");
    }

    [SerializeField]
    protected string _res;

    public bool IsExp
    {
        get
        {
            return dropItemType == DropItemType.Exp;
        }
    }
    public bool IsCoin
    {
        get
        {
            return dropItemType == DropItemType.Coin;
        }
    }
    public bool IsChest
    {
        get
        {
            return dropItemType == DropItemType.Chest;
        }
    }
    public float PercentValue
    {
        get
        {
            return value / 100f;
        }
    }
    public string GetRes()
    {
        return _res;
    }
}