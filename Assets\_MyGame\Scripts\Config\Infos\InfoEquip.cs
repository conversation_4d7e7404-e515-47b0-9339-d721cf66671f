﻿using System;
using LitJson;
[Serializable]
public class InfoEquip : ConfigData
{
    public int equipSubType;
    public int quality;
    public int group;
    public string name;

    public override object key { get => equipSubType +"_"+ quality + "_" + group; protected set => base.key = value; }
    override public void Parse(JsonData data)
    {
        equipSubType = JsonUtil.ToInt(data, "equipSubType");
        quality = JsonUtil.ToInt(data, "quality");
        group = JsonUtil.ToInt(data, "group");
        name = JsonUtil.ToString(data, "name");
    }
}