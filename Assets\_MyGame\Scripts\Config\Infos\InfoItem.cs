﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LitJson;
using FairyGUI;
[Serializable]
public class InfoItem : ConfigData
{
    public string name;
    public string desc;
    public int id;
    public string icon;
    public int itemType;
    public int modelId;
    public int buyPrice;
    public bool canGive;
    public int giveMax;
    public int buyCurrencyType;
    public bool isGetUse;
    public bool isUse;
    public string itemEffect;
    public bool canUseInBag;
    public bool showReward;
    public int useReqParam1;
    public int quality;
    public long itemCount;
    public int showModeId;
    public string rewardDesc;
    public int useReqParam0;
    public string optionalBox;

    override public void Parse(JsonData data)
    {
        name = JsonUtil.ToString(json, "name");
        desc = JsonUtil.ToString(json, "desc");
        id = JsonUtil.ToInt(json, "id");
        icon = JsonUtil.ToString(json, "icon");
        itemType = JsonUtil.ToInt(json, "itemType");
        modelId = JsonUtil.ToInt(json, "modelId");
        buyPrice = JsonUtil.ToInt(json, "buyPrice");
        canGive = JsonUtil.ToBool(json, "canGive");
        giveMax = JsonUtil.ToInt(json, "giveMax");
        buyCurrencyType = JsonUtil.ToInt(json, "buyCurrencyType");
        isGetUse = JsonUtil.ToBool(json, "isGetUse");
        isUse = JsonUtil.ToBool(json, "isUse");
        itemEffect = JsonUtil.ToString(json, "itemEffect");
        canUseInBag = JsonUtil.ToBool(json, "canUseInBag");
        showReward = JsonUtil.ToBool(json, "showReward");
        useReqParam1 = JsonUtil.ToInt(json, "useReqParam1");
        quality = JsonUtil.ToInt(json, "quality");
        showModeId = JsonUtil.ToInt(json, "showModelId");
        rewardDesc = JsonUtil.ToString(json, "rewardDesc");
        useReqParam0 = JsonUtil.ToInt(json, "useReqParam0");
        optionalBox = JsonUtil.ToString(json, "optionalBox");
    }

    public string iconUrl
    {
        get
        {
            return "ui://Icon/" + this.icon;
        }
    }

    public string iconHeroUrl
    {
        get
        {
            return "ui://Icon/hero" + this.icon;
        }
    }

    public string itemEffectUrl
    {
        get
        {
            return "ui://Icon/" + this.itemEffect;
        }
    }

    public string qualityUrl
    {
        get
        {
            return "ui://Icon/image_equipBg" + this.quality;
        }
    }
    public void IconnAndQuality(GComponent Gcom)
    {
        if(Gcom != null)
        {
            var icon = Gcom.GetChild("icon").asLoader;
            if(icon != null)
            {
                icon.url = iconUrl;
            }
            var qualityBg = Gcom.GetChild("qualityBg").asLoader;
            if (qualityBg != null)
            {
                qualityBg.url = qualityUrl;
            }
        }
    }
}


