﻿using System;
using LitJson;
[Serializable]
public class InfoMap : ConfigData
{
    public int id;
    public string name;
    public string res;
    public int maxMonsterCount;
    public string bgMusic;
    public float collisionExpPercent;
    public int type;
    public int gatePage;
    public int isShowChangeSeat;

    public override object key { get => id; protected set => base.key = value; }
    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        name = JsonUtil.ToString(data, "name");
        res = JsonUtil.ToString(data, "res");
        maxMonsterCount = JsonUtil.ToInt(data, "maxMonsterCount");
        bgMusic = JsonUtil.ToString(data, "bgMusic");
        collisionExpPercent = JsonUtil.ToFloat(data, "collisionExpPercent");
        gatePage = JsonUtil.ToInt(data, "gatePage");
        type = JsonUtil.ToInt(data, "type");
        isShowChangeSeat = JsonUtil.ToInt(data, "isShowChangeSeat");
    }

}