﻿using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;
using UnityEngine.UIElements;

[Serializable]
public class InfoMonster : ConfigData
{
    public int id;
    public string name;
    public string res;
    public int[] weaponIds;
    public int dieWeaponId;
    public bool ignoreKnockback;
    public string icon;
    public int type;                //怪物类型
    public float critical;          //暴击率
    public float dodge;             //闪避率
    public float moveSpeed;         //移动速度
    public float attackCd;          //攻击间隔
    public int moveTargetAreaIndex;     //移动目标区域索引，如果是碰撞类的，设置成-1
    public string dieEffect;        //死亡特效
    public int dropAddSkillId;          //秘宝掉落DropId
    public float dropAddSkillPro;       //秘宝再次掉落概率   
    public string buffEffectScale;

    public int gateEntryEffectType;         //关卡词条效果类型
    public string desc;
    public float scale;
    public float uiScale;

    public override object key { get => id; protected set => base.key = value; }
    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        name = JsonUtil.ToString(data, "name");
        res = JsonUtil.ToString(data, "res");
        var weaponIdStr = JsonUtil.ToString(data, "weaponId");
        if (!string.IsNullOrEmpty(weaponIdStr))
        {
            var ids = weaponIdStr.Split('|');
            var count = ids.Length;
            weaponIds = new int[count];
            for (int i = 0; i < ids.Length; i++)
            {
                weaponIds[i] = int.Parse(ids[i]);
            }
        }
        else
        {
            weaponIds = new int[] { };
        }

        type = JsonUtil.ToInt(data, "type");
        dieWeaponId = JsonUtil.ToInt(data, "dieWeaponId");
        ignoreKnockback = JsonUtil.ToInt(data, "ignoreKnockback") == 1;
        icon = JsonUtil.ToString(data,"icon");
        critical = JsonUtil.ToFloat(data, "critical");
        dodge = JsonUtil.ToFloat(data, "dodge");
        moveSpeed = JsonUtil.ToFloat(data, "moveSpeed");
        attackCd = JsonUtil.ToFloat(data, "attackCd");
        moveTargetAreaIndex = JsonUtil.ToInt(data, "moveTargetAreaIndex");
        dieEffect = JsonUtil.ToString(data, "dieEffect");
        dropAddSkillId = JsonUtil.ToInt(data, "dropAddSkillId");
        dropAddSkillPro = JsonUtil.ToFloat(data, "dropAddSkillPro");
        buffEffectScale = JsonUtil.ToString(data, "buffEffectScale");
        gateEntryEffectType = JsonUtil.ToInt(data, "gateEntryEffectType");
        desc = JsonUtil.ToString(data, "desc");
        scale = JsonUtil.ToFloat(data, "scale");
        uiScale = JsonUtil.ToFloat(data, "uiScale");
        if (uiScale == 0)
        {
            uiScale = 180;
        }
#if UNITY_EDITOR
        //编辑器下验证数据正确性
        GetBuffOffsetAndScale(string.Empty);
#endif
    }

    private Dictionary<string, Vector4> buffEffectScaleMap;
    /// <summary>
    /// 对应buff的偏移和缩放
    /// </summary>
    /// <param name="effectName"></param>
    /// <returns> Vector4(offsetX,offsetY,scaleX,scaleY)</returns>
    internal Vector4? GetBuffOffsetAndScale(string effectName)
    {
        if (buffEffectScaleMap == null)
        {
            try
            {
                buffEffectScaleMap = new Dictionary<string, Vector4>();
                var effectAry = buffEffectScale.Split('|');
                for (int i = 0; i < effectAry.Length; i++)
                {
                    var tempAry = effectAry[i].Split(',');
                    if (tempAry.Length == 5)
                    {
                        buffEffectScaleMap[tempAry[0]] = new Vector4(
                            float.Parse(tempAry[1]),//offsetX
                            float.Parse(tempAry[2]),//offsetY
                            float.Parse(tempAry[3]),//scaleX
                            float.Parse(tempAry[4]));//scaleY
                    }
                }
            }
            catch (System.Exception)
            {
                Log.Warning($"[InfoMonster] monsterId：{id}  invalid buffEffectScale: {buffEffectScale}  ");
            }
        }

        if (buffEffectScaleMap.TryGetValue(effectName, out Vector4 result))
        {
            return result;
        }
        return null;
    }
}