﻿using System;
using LitJson;
[Serializable]
public class InfoSkill : ConfigData
{
    public int id;
    public int preId;               //前置技能
    public int nextId;              //下一技能
    public int level;               //等级
    public int link;                //所在的技能链
    public int type;                //技能类型
    public string name;
    public string desc;
    public string icon;
    public int effectType;          //效果类型
    public int effectParam0;
    public int effectParam1;
    public string strParams;     //字符串参数
    public int relatedLink;         //关联的技能链，如果是被动类技能需要与技能绑定，则需要配置这个
    public int relatedHeroId;       //关联的英雄ID
    public int quality;
    public string userEffectAni;        //使用者特效动画
    public string targetEffectAni;      //目标者特效动画
    public int superType;                //是否是超武
    public int randomPro;               //随机权重

    public bool IsLastSkill()
    {
        if (this.preId > 0 && this.nextId <= 0)
        {
            return true;
        }
        return false;
    }

    //超武
    public bool IsSuperSkill()
    {
        return superType == 1;
    }

    //必杀技
    public bool IsUniqueSkill()
    {
        return superType == 2;
    }

    public override object key { get => id; protected set => base.key = value; }

    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        preId = JsonUtil.ToInt(data, "preId");
        nextId = JsonUtil.ToInt(data, "nextId");
        level = JsonUtil.ToInt(data, "level");
        link = JsonUtil.ToInt(data, "link");
        type = JsonUtil.ToInt(data, "type");
        name = JsonUtil.ToString(data, "name");
        desc = JsonUtil.ToString(data, "desc");
        icon = JsonUtil.ToString(data, "icon");
        effectType = JsonUtil.ToInt(data, "effectType");
        effectParam0 = JsonUtil.ToInt(data, "effectParam0");
        effectParam1 = JsonUtil.ToInt(data, "effectParam1");
        relatedLink = JsonUtil.ToInt(data, "relatedLink");
        relatedHeroId = JsonUtil.ToInt(data, "relatedHeroId");
        quality = JsonUtil.ToInt(data, "quality");
        strParams = JsonUtil.ToString(data, "strParams");
        userEffectAni = JsonUtil.ToString(data, "userEffectAni");
        targetEffectAni = JsonUtil.ToString(data, "targetEffectAni");
        superType = JsonUtil.ToInt(data, "superType");
        randomPro = JsonUtil.ToInt(data, "randomPro");
    }
}