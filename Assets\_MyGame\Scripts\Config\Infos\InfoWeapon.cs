﻿using LitJson;
using System;
[Serializable]
public class InfoWeapon : ConfigData
{
    public int id;
    public string name;
    public string desc;
    public bool isActiveSkill;//是否是主动技能
    public int level;
    public string icon;
    public int weaponGroup = 0;             //武器组，同一组的武器装备的时候会替换前一个
    public string emitorRes;                //发射器
    public int bulletId;                    //子弹ID
    public WeaponFollowType followType;     //发射器的跟随类型
    public int relatedSkillLink;            //武器关联的技能链


    public override object key { get => id; protected set => base.key = value; }

    public override void Parse(JsonData data)
    {
        id = JsonUtil.ToInt(data, "id");
        name = JsonUtil.ToString(data, "name");
        desc = JsonUtil.ToString(data, "desc");
        isActiveSkill = JsonUtil.ToInt(data, "isActiveSkill") == 1;
        level = JsonUtil.ToInt(data, "level");
        icon = JsonUtil.ToString(data, "icon");
        weaponGroup = JsonUtil.ToInt(data, "weaponGroup");
        emitorRes = JsonUtil.ToString(data, "emitorRes");
        bulletId = JsonUtil.ToInt(data, "bulletId");
        followType = (WeaponFollowType)JsonUtil.ToInt(data, "followType");
        relatedSkillLink = JsonUtil.ToInt(data, "relatedSkillLink");
    }
}