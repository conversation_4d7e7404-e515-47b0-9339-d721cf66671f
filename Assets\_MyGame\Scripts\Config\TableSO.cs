using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;

[Serializable]
public class TableSO : ScriptableObject
{
    //以下逻辑只在编辑器中有效
#if UNITY_EDITOR
    public static string[] tableNames = new string[] {
        "Equip",
        "GateTemplate",
        "Monster",
        "Weapon",
        "Bullet",
        "Level",
        "DropItem",
        "Map",
        "Buff",
        // "Pet",
        "Item",
        "Langui",
        "LangError",
        "Skill",
        "Entry",
        "Guide",
        "UserAgreement",
        "Share",
        "ResourceGuide",
        "GateMonsterShow",
        "HelpTips",
        "BattleGuide",
        "BorderInfo",
        "CardFrame",
        "TreasureRaidersMonsterWava",
        "TreasureRaidersBuffItem",
        "MidWayMonsterWave",
        "MidWayBuffItem",
        "FlyBulletBuffItem",
        "FlyBulletMonsterWave",
        "dragonMonsters",
        "dragonBuffItems",
        "DragonGearMonster",
        "DragonGearH<PERSON>",
        "DragonGearRate",
        "hero_battle_buff_effect_config",
        "hero_battle_skill_config",
        "ChatSticker",
        "ChatDeco",
        "RoleName",
    };

    public static ScriptableObject GenerateSO(string tableName, JsonData json)
    {
        switch (tableName)
        {
            case "Equip":
                equips = CreateArray<InfoEquip>(json);
                break;
            case "GateTemplate":
                gateTemplates = CreateArray<InfoGateTemplate>(json);
                break;
            case "Monster":
                monsters = CreateArray<InfoMonster>(json);
                break;
            case "Weapon":
                weapons = CreateArray<InfoWeapon>(json);
                break;
            case "Bullet":
                bullets = CreateArray<InfoBullet>(json);
                break;
            case "Level":
                levels = CreateArray<InfoLevel>(json);
                break;
            case "DropItem":
                dropItems = CreateArray<InfoDropItem>(json);
                break;
            case "Map":
                maps = CreateArray<InfoMap>(json);
                break;
            case "Buff":
                buffs = CreateArray<InfoBuff>(json);
                break;
            case "Pet":
                pets = CreateArray<InfoPet>(json);
                break;
            case "Item":
                items = CreateArray<InfoItem>(json);
                break;
            case "Langui":
                languis = CreateArray<InfoLang>(json);
                break;
            case "LangError":
                langEffors = CreateArray<InfoLang>(json);
                break;
            case "Skill":
                skills = CreateArray<InfoSkill>(json);
                break;
            case "Entry":
                entries = CreateArray<InfoEntry>(json);
                break;
            case "Guide":
                guides = CreateArray<InfoGuide>(json);
                break;
            case "UserAgreement":
                userAgreements = CreateArray<InfoUserAgreement>(json);
                break;
            case "Share":
                shares = CreateArray<InfoShare>(json);
                break;
            case "ResourceGuide":
                resourceGuides = CreateArray<InfoResourceGuide>(json);
                break;
            case "GateMonsterShow":
                gateMonsterShows = CreateArray<InfoGateMonsterShow>(json);
                break;
            case "HelpTips":
                helpTips = CreateArray<InfoHelpTip>(json);
                break;
            case "BattleGuide":
                return LoadFromJson<BattleGuideSo, InfoBattleGuide>(json);
            case "BorderInfo":
                borderInfos = CreateArray<InfoBorderInfo>(json);
                break;
            case "CardFrame":
                cardFrames = CreateArray<InfoCardFrame>(json);
                break;
            case "FlyBulletMonsterWave":
                flyMonsterWava = CreateArray<InfoFlyBulletMonster>(json);
                break;
            case "FlyBulletBuffItem":
                flyBulletBuffItems = CreateArray<InfoFlyBulletBuffItem>(json);
                break;
            case "MidWayBuffItem":
                midWayBuffItems = CreateArray<InfoMidWayBuffItem>(json);
                break;
            case "MidWayMonsterWave":
                midWayMonsterWava = CreateArray<InfoMidWayMonster>(json);
                break;
            case "TreasureRaidersBuffItem":
                treasureRaidersBuffItems = CreateArray<InfoTreasureRaidersBuffItem>(json);
                break;
            case "TreasureRaidersMonsterWava":
                treasureRaidersMonsterWava = CreateArray<InfoTreasureRaidersMonster>(json);
                break;
            case "dragonMonsters":
                dragonMonsters = CreateArray<InfoDragonMonster>(json);
                break;
            case "dragonBuffItems":
                dragonBuffItems = CreateArray<InfoDragonBuffItem>(json);
                break;
            case "DragonGearMonster":
                dragonGearMonsters = CreateArray<InfoDragonGearMonster>(json);
                break;
            case "DragonGearHero":
                dragonGearHeros = CreateArray<InfoDragonGearHero>(json);
                break;
            case "DragonGearRate":
                dragonGearRates = CreateArray<InfoDragonGearRate>(json);
                break;
            case "hero_battle_skill_config":
                pvpskills = CreateArray<InfoPVPSkill>(json);
                break;
            case "hero_battle_buff_effect_config":
                pvpBuffs = CreateArray<InfoPVPBuff>(json);
                break;
            case "ChatSticker":
                stickers = CreateArray<InfoSticker>(json);
                break;
            case "ChatDeco":
                chatDecos = CreateArray<InfoChatDeco>(json);
                break;
            case "RoleName":
                roleNames = CreateArray<InfoRoleName>(json);
                break;
        }

        return null;
    }

    public class BattleGuideSo : BaseSO { public new InfoBattleGuide[] datas; }

    public static BaseSO LoadFromJson<So, Info>(JsonData json) where So : BaseSO, new() where Info : ConfigData, new()
    {
        var so = CreateInstance<So>();
        so.datas = TableSO.CreateArray<Info>(json);
        return so as So;
    }

    public static T[] CreateArray<T>(JsonData json) where T : ConfigData, new()
    {
        var list = new List<T>();
        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Init(i, json[i]);
                data.uniqueKey = data.key.ToString();
                list.Add(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Init(key, json[key]);
                data.uniqueKey = data.key.ToString();
                list.Add(data);
            }
        }
        var result = list.ToArray();
        return result;
    }
#endif
}